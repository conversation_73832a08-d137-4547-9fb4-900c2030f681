## task crawl
1. crawl dữ liệu tỉnh thành(tỉnh) mớ<PERSON>, lư<PERSON> thông tin vào bảng ___province
2. crawl dữ liệu xã phường(xã) mới, lưu thông tin vào bảng ward
-  map dữ liệu tỉnh và xã mới. 1 tỉnh có nhiều xã. 1 xã chỉ thuộc 1 tỉnh
3. crawl dữ liệu geometry của tỉnh, lưu thông tin vào bảng geo_province
4. crawl dữ liệu geometry của xã, lưu thông tin vào bảng geo_ward.geometry
## Task Database
5. bảng ___province chứa tỉnh cũ và mới, tỉnh mới sẽ bao gồm các tỉnh cũ.Một số tỉnh sẽ không sáp nhập mà giữ nguyên. ví dụ: Ninh Bình mới sẽ được hợp thành từ 3 tỉnh: <PERSON><PERSON>, <PERSON><PERSON>, Nam Định => c<PERSON>n thêm cột thông tin new_province_id để lưu ID của tỉnh mới ở các tỉnh thành cũ, t<PERSON><PERSON> thành mới và cũ được phân biệt bởi cột is_merge = 2( tỉnh mới)
6. b<PERSON>ng ward chứa xã cũ và mới, tương tự như task số 5 tuy nhiên sẽ không có xã nào giữ nguyên mà sẽ được hợp nhất lại thành xã lớn hơn, cần biết xã mới được hợp thành từ các xã cũ nào
7. bảng brand_office, đồng bộ lại dữ liệu lat, long cho chính xác
8. bảng brand_store, đồng bộ lại dữ liệu lat, long cho chính xác
9. bảng brand_office, sau khi đã đồng bộ lại dữ liệu lat,long. dựa vào dữ liệu geometry lấy được ở task số 4, sử dụng thư viện để xem tọa độ của bản ghi thuộc địa phận xã, phường nào, sau đó cập nhật lại vào cột title. Cột title hiện tại là thông tin đầy đủ số nhà, tên dường(khu dân cư), xã/phường, quận/huyện, tỉnh/thành phố. yêu cầu chỉ cập nhật lại xã phường và tỉnh thành, bỏ quận, huyện. giữ nguyên số nhà, tên đường, khu dân cư
10. bảng brand_store, lên task tương tự như task 9
11. bảng gift_receiver: thêm cột để đánh dấu sử dụng dữ liệu mới( hoặc có thể đánh dấu từ ID hoặc created nào sẽ sử dụng dữ liệu mới)
12. bảng address: thêm task tương tự như task 11
13. bảng cart: đang phân tích
14. bảng cart_detail: đang phân tích
15. bảng province_partner: Đang phân tích
## task API UrBox
16. viết API lấy danh sách tỉnh mới
17. viết API lấy danh sách xã dựa trên ID tỉnh
18. cập nhật whitelabels
19. cập nhật API phục vụ đối tác(CPV)
20. cập nhật API cho APP
## task FE
21. cập nhật giftlink V3,5,6,8,10
22. Cập nhật trang đổi quà vật lý
23. Cập nhật giao diện điền thông tin địa chỉ trên các trang whitelabel
24. Cập nhật logic xử lý và hiển thị trên HUB
25. Cập nhật logic xử lý và hiển thị trên portal UC
## API với đối tác
26. Nhanh
27. ....

### ====================================

# Phương án chi tiết
## Phase 1:Xử lý DB
- Import dữ liệu tỉnh, xã mới:(done)
    + Bỏ bảng district(done)
    + Đối với bảng __province, ward:
        + Thêm field is_new(int) để đánh dấu là xã phường mới/cũ( 1= cũ, 2 = mới). Done
        + Thêm field __province.new_pti_id(int) field này sử dụng ở các tỉnh cũ. Nó dùng để biết được tỉnh cũ này thuộc về tỉnh nào sau khi sáp nhập. Ví dụ: Ninh Bình cũ sẽ có new_pti_id = 37 (Ninh Bình mới) Done
- Chạy tool đồng bộ lại địa chỉ ở bảng brand_office và brand_store (In Process)
    + Chạy tool để lấy lại latidute, longditude chính xác bằng api của google( Not start)
    + Viết tool để map lại thông tin trong bảng brand_office, brand_store các thông tin: address, ward_id, city_id thành thông tin mới.(Done)
        + Sử dụng tọa độ lat, long để kiểm tra với thông tin geometry coodinate của Cục Đo đạc, Bản đồ để kiểm tra xem tọa độ này thuộc về đâu. => cập nhật vào DB.(Done).
        + Sử dụng GeminiAI để chuyển đổi địa chỉ cũ thành địa chỉ mới. với cấu trúc: [số nhà, tên đường, khu dân cư], [xã/phường], [tỉnh/thành phố] (Done)
- Đối với các bảng khác có sử dụng tới thông tin city_id, ward_id,district_id: Not start
    + Bảng cart, cart_detail, province_partner, address, gift_receiver ...
    + Căn cứ vào ID của bảng ___province để xác định đó là địa chỉ mới hay cũ.
        - Hiện tại, bảng ___province có ID >=82 sẽ là các tỉnh thành mới => đề xuất nếu cần filter ở các bảng trong DB urbox thì sẽ căn cứ vào ID >=82 => xử lý theo logic của tình thành mới( chỉ filter theo city_id, ward_id, bỏ qua district_id)
## Phase 2: Chạy pilot trên điền thông tin nhận quà vật lý + 1 trang whitelabel
- Cập nhật logic xử lý trên HUB:
    + Trên Hub có phần xử lý giao hàng và module hiển thị địa chỉ giao hàng đối với quà vật lý:
        + Đối với các đơn hàng cũ thì sẽ hiển thị địa chỉ cũ
        + Đối với các đơn hàng mới thì sẽ hiển thị địa chỉ mới và hiển thị tag (Địa chỉ mới) để người dùng biết đây là địa chỉ mới.
        + Đối với các form cập nhật địa chỉ giao hàng của CS: Đối với các đơn hàng sử dụng địa chỉ cũ => Hiển thị form nhập thông tin đang sử dụng, đối với các đơn sử dụng địa chỉ mới => Cần thay đổi giao diện để CS thao tác với xã/ tỉnh mới.

- Cập nhật FE để hiển thị địa chỉ mới: Not start
    + Trang nhận quà vật lý: Not start
    + Trang whitelabel: Not start - Chọn Sabre

## Phase 3: Cập nhật API Và Whitelabel
- Các api hiện tại sẽ cần cập nhật để sử dụng dữ liệu mới: Not start
    + Phương án 1: Bổ sung thêm param vào API cũ(is_new) để FE gửi lên API lấy địa chỉ cũ hay mới. Ở đầu API, trước mắt setup mặc định is_new = 1(Mặc định lấy địa chỉ cũ). Sau khi chuyển hoàn toàn sang địa chỉ mới thì mới chuyển is_new = 2
    + Phương án 2: Tách thành 2 API riêng biệt, 1 API lấy địa chỉ cũ, 1 API lấy địa chỉ mới
- Cập nhật FE để hiển thị địa chỉ mới: Not start
    + Thêm tùy chọn để khách hàng chọn nhập địa chỉ mới/cũ trên form.

## Phase4: Cập nhật API đối tác.
- nhanh.vn: Chưa phân tích...
- .....