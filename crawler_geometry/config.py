# Configuration file for ward geometry crawler

# Database configuration
import random


DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'urbox',
    'charset': 'utf8mb4'
}

# API configuration
API_CONFIG = {
    'base_url': 'https://vnsdi.mae.gov.vn/basemap/rest/services/TraCuuDVHC/MapServer/1/query',
    'token': "ExStvSdFXfM9AqU46uMern9qr2wUUi-i_ks01eTOCqyM-Ckh6nCDxhsqmS05JaFqzUSipH3bzYU7hEGDZVRaBA..",
    'headers': {
        'accept': '*/*',
        'referer': 'https://vnsdi.mae.gov.vn/bandonen/',
        # <PERSON>ie từ Postman - cần thiết để API hoạt động đúng và không bị cache
        'Cookie': 'AGS_ROLES="sym-gcm|1|TXtDNYdmomeekVI2ru2cMQ==|1t6RPH-lusab2R-esK5ZdMBeZqbJe6NFjrMgEeNpDB2xQj__g6KeaESb6MafT-w-A8kX"'
    },
    'params': {
        'returnGeometry': 'true',
        'where': '1=1',
        'outSR': '4326',
        'outFields': '*',
        'inSR': '4326',
        'geometryType': 'esriGeometryPoint',
        'spatialRel': 'esriSpatialRelWithin',
        'f': 'json'
    }
}

# Output configuration
OUTPUT_CONFIG = {
    'results_file': 'crawled_data/wards/ward_geometry_results.json',
    'log_file': 'crawled_data/wards/crawl_ward.log',
    'failed_records_file': 'crawled_data/wards/failed_records.json',
    'retry_results_file': 'crawled_data/wards/retry_results.json',
    'wards_dir': 'crawled_data/wards/ward',  # Thư mục cho individual files
    'batch_size': 100,  # Number of records to process in each batch
    'request_delay': random.uniform(0, 0.2),  # Delay between requests (seconds)
    'max_retries': 3,  # Maximum retry attempts for failed records
    'retry_delay': 1.0  # Delay between retry attempts (seconds)
} 