# 🏡 WARD CRAWLER TOOLKIT - HƯỚNG DẪN SỬ DỤNG

## 📋 Tổng quan

**Ward Crawler Toolkit** là công cụ hoàn chỉnh để crawl dữ liệu geometry xã từ API VNSDI (Vietnam National Spatial Data Infrastructure) - thực hiện Task số 4.

### 🎯 Chức năng chính:
1. **Crawl dữ liệu ban đầu** - Crawl tất cả bản ghi từ bảng geo_ward
2. **Retry các bản ghi failed** - Tự động retry và cập nhật kết quả
3. **Xem báo cáo tổng quan** - Thống kê chi tiết
4. **Quản lý sessions** - Tổ chức và chuyển đổi giữa các session
5. **Session-based workflow** - Tách biệt các lần crawl

---

## 🚀 Cách sử dụng

### Khởi chạy toolkit:
```bash
cd crawl_ward
python ward_crawler_toolkit.py
```

### <PERSON><PERSON> ch<PERSON>:
```
🎯 MENU CHÍNH:
1. 🔄 Crawl dữ liệu ban đầu
2. 🔁 Retry các bản ghi failed
3. 📊 Xem báo cáo tổng quan
4. 🔧 Chọn session làm việc
5. 📁 Quản lý sessions
0. ❌ Thoát
```

---

## 📖 Hướng dẫn từng chức năng

### 1. 🔄 Crawl dữ liệu ban đầu

**Mục đích:** Crawl dữ liệu geometry từ tất cả bản ghi trong bảng geo_ward

**Cách sử dụng:**
- Chọn option `1` từ menu
- Tool sẽ tự động kết nối database và crawl từng bản ghi
- **Không retry ngay** - chỉ thông báo có bao nhiêu failed records

**Kết quả:**
- Tạo session mới với timestamp (VD: `crawl_session_20250716_141100`)
- Lưu dữ liệu JSON cho từng bản ghi thành công
- Tạo file `ward_geometry_results.json` với tổng kết
- Lưu failed records vào `failed_records.json` để retry sau

### 2. 🔁 Retry các bản ghi failed

**Mục đích:** Retry lại các bản ghi bị lỗi và tự động cập nhật kết quả

**Cách sử dụng:**
- Chọn option `2` từ menu
- Tool sẽ tự động tìm failed records từ session hiện tại
- **Hỏi xác nhận** trước khi retry
- **Tự động cập nhật kết quả** sau khi retry

**Tính năng đặc biệt:**
- ✅ **Tích hợp update result** - Không cần chạy update thủ công
- ✅ **Intelligent retry** - Retry max 3 lần với delay
- ✅ **Tự động merge** - Kết quả retry merge vào file chính
- ✅ **Session-based** - Chỉ retry failed records của session hiện tại

### 3. 📊 Xem báo cáo tổng quan

**Mục đích:** Hiển thị thống kê chi tiết về session hiện tại

**Thông tin hiển thị:**
- Số lượng bản ghi thành công vs thất bại
- Tỷ lệ thành công
- Top 5 bản ghi thành công
- Danh sách bản ghi thất bại (nếu có)
- Thông tin session và thời gian

### 4. 🔧 Chọn session làm việc

**Mục đích:** Chuyển đổi giữa các session khác nhau

**Cách sử dụng:**
- Chọn option `4` từ menu
- Chọn session từ danh sách
- Hoặc tạo session mới với option `n`
- Tool sẽ chuyển sang session được chọn

### 5. 📁 Quản lý sessions

**Mục đích:** Quản lý, xóa, theo dõi các session

**Chức năng:**
- Liệt kê tất cả sessions với thông tin chi tiết
- Hiển thị session hiện tại
- Xóa session không cần thiết
- Kiểm tra kích thước file kết quả

---

## 🌟 Tính năng nâng cao

### Session-based Workflow

**Lợi ích:**
- Tách biệt các lần crawl khác nhau
- Có thể retry sau này thay vì ngay lập tức
- Dễ dàng theo dõi và quản lý kết quả
- Backup tự động cho mỗi session

**Cách hoạt động:**
1. Mỗi session có thư mục riêng
2. Kết quả được lưu theo session
3. Có thể chuyển đổi giữa các session
4. Failed records được lưu trữ để retry sau

### Intelligent Retry System

**Đặc điểm:**
- Retry max 3 lần cho mỗi bản ghi
- Delay 1 giây giữa các lần retry
- Tự động merge kết quả
- Comprehensive logging
- Error categorization

---

## 📂 Cấu trúc thư mục

```
crawl_ward/
├── ward_crawler_toolkit.py      # Toolkit chính
├── crawl_ward_geometry.py       # Crawl module
├── retry_failed_records.py      # Retry module
├── test_retry_mechanism.py      # Test retry
├── test_toolkit.py             # Test toolkit
├── config.py                   # Configuration
├── requirements.txt            # Dependencies
└── exports/
    └── sessions/               # Thư mục sessions
        └── crawl_session_YYYYMMDD_HHMMSS/
            ├── session_info.json       # Thông tin session
            ├── results/               # Kết quả crawl
            │   ├── ward_geometry_results.json
            │   ├── failed_records.json
            │   └── retry_results.json
            └── logs/                  # Log files
                └── crawl_ward.log
```

---

## 🔧 So sánh với cách cũ

### Trước (cách cũ):
```bash
# Crawl
python run_crawl.py --test

# Retry ngay lập tức
python run_crawl.py --retry

# Kết quả lưu trực tiếp vào exports/
```

### Sau (cách mới):
```bash
# Toolkit với menu
python ward_crawler_toolkit.py

# Chọn session và crawl
# Retry sau khi cần
# Kết quả lưu theo session
```

### Lợi ích cải tiến:
- ✅ **Tách biệt retry** - Không retry ngay, có thể retry sau
- ✅ **Session management** - Tổ chức tốt hơn
- ✅ **Menu system** - Dễ sử dụng
- ✅ **Flexible workflow** - Linh hoạt trong sử dụng
- ✅ **Better organization** - Cấu trúc thư mục rõ ràng

---

## 🎯 Workflow khuyến nghị

### Lần đầu sử dụng:
1. Chạy `python ward_crawler_toolkit.py`
2. Tool tự động tạo session mới
3. Chọn `1` để crawl dữ liệu ban đầu
4. Chọn `3` để xem báo cáo
5. Nếu có failed records → Ghi nhớ, retry sau

### Sử dụng hàng ngày:
1. Chạy `python ward_crawler_toolkit.py`
2. Chọn `4` để chọn session cần làm việc
3. Chọn `2` để retry failed records
4. Chọn `3` để xem báo cáo cập nhật

### Quản lý dữ liệu:
1. Chọn `5` để quản lý sessions
2. Xóa sessions cũ không cần thiết
3. Kiểm tra kích thước file kết quả

---

## 🔍 Troubleshooting

### Lỗi kết nối database:
- Kiểm tra cấu hình trong `config.py`
- Đảm bảo database đang chạy
- Kiểm tra bảng `geo_ward` tồn tại

### Session không tìm thấy:
- Chạy option `1` để tạo session mới
- Hoặc chọn option `4` để chọn session khác

### Failed records không retry được:
- Kiểm tra file `failed_records.json` trong session
- Đảm bảo session đã có dữ liệu crawl trước đó

---

## 📊 Kết quả mong đợi

### Tỷ lệ thành công: **95-100%**
### Tổng số bản ghi: **~5-10 bản ghi (test)**
### Formats output:
- **JSON:** Dữ liệu thô từ API
- **Log:** Chi tiết quá trình crawl
- **Session info:** Metadata session

---

## 🎉 Tính năng đã tích hợp

### ✅ Session-based workflow
- Tự động tạo session với timestamp
- Tổ chức dữ liệu theo session
- Chuyển đổi giữa các session

### ✅ Retry mechanism tách biệt
- Không retry ngay lập tức
- Có thể retry sau khi cần
- Tự động merge kết quả

### ✅ Menu system
- Interactive menu dễ sử dụng
- Workflow suggestions
- Clear navigation

### ✅ Comprehensive management
- Session management
- Data organization
- Progress tracking

---

**🚀 Sẵn sàng sử dụng! Chạy `python ward_crawler_toolkit.py` để bắt đầu.** 