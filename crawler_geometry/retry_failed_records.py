#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script retry cho các bản ghi crawl thất bại
"""

import asyncio
import json
import time
from datetime import datetime
import aiohttp
from crawl_ward_geometry import WardGeometryCrawler
from config import OUTPUT_CONFIG


class RetryFailedRecords:
    """Class để retry các bản ghi crawl thất bại"""
    
    def __init__(self):
        self.crawler = WardGeometryCrawler()
        self.retry_count = 0
        self.max_retries = OUTPUT_CONFIG['max_retries']
        self.retry_delay = OUTPUT_CONFIG['retry_delay']
        
    def get_failed_records(self):
        """<PERSON><PERSON><PERSON> danh sách failed records từ file"""
        failed_records = self.crawler.load_failed_records()
        if not failed_records:
            self.crawler.logger.info("Không có bản ghi thất bại nào để retry")
            return []
            
        self.crawler.logger.info(f"T<PERSON><PERSON> thấy {len(failed_records)} bản ghi thất bại cần retry")
        return failed_records
        
    async def retry_single_record(self, session, record):
        """Retry crawl cho một bản ghi (async version)"""
        max_attempts = self.max_retries

        for attempt in range(1, max_attempts + 1):
            self.crawler.logger.info(f"Retry attempt {attempt}/{max_attempts} cho ward_id={record['id']}")

            try:
                # Tạo lại cấu trúc record để phù hợp với crawler
                retry_record = {
                    'id': record['id'],
                    'longitude': record['longitude'],
                    'latitude': record['latitude'],
                    'ward_title': record.get('ward_title'),
                    'province_title': record.get('province_title')
                }

                # Thử crawl async
                success = await self.crawler.crawl_single_record(session, retry_record)
                
                if success:
                    self.crawler.logger.info(f"Retry thành công cho ward_id={record['id']} ở attempt {attempt}")
                    return True
                else:
                    self.crawler.logger.warning(f"Retry thất bại cho ward_id={record['id']} ở attempt {attempt}")
                    
            except Exception as e:
                self.crawler.logger.error(f"Lỗi khi retry ward_id={record['id']} attempt {attempt}: {e}")
                
            # Delay giữa các attempt
            if attempt < max_attempts:
                self.crawler.logger.info(f"Chờ {self.retry_delay}s trước attempt tiếp theo...")
                time.sleep(self.retry_delay)
                
        self.crawler.logger.error(f"Retry thất bại hoàn toàn cho ward_id={record['id']} sau {max_attempts} attempts")
        return False
        
    async def retry_all_failed_records(self):
        """Retry tất cả các bản ghi thất bại (async version)"""
        try:
            self.crawler.logger.info("🔄 Bắt đầu retry các bản ghi thất bại (async mode)...")

            # Lấy danh sách failed records
            failed_records = self.get_failed_records()

            if not failed_records:
                return True

            # Reset crawler results
            self.crawler.results = []
            self.crawler.failed_records = []

            total_records = len(failed_records)
            success_count = 0

            # Tạo aiohttp session
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=60)

            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                # Retry từng bản ghi với concurrency nhỏ hơn (5 concurrent)
                semaphore = asyncio.Semaphore(5)

                async def retry_with_semaphore(record, index):
                    async with semaphore:
                        self.crawler.logger.info(f"🔄 Retry bản ghi {index}/{total_records}: ward_id={record['id']}")
                        success = await self.retry_single_record(session, record)
                        if success:
                            return 1
                        return 0

                # Tạo tasks cho tất cả failed records
                tasks = []
                for i, record in enumerate(failed_records, 1):
                    task = retry_with_semaphore(record, i)
                    tasks.append(task)

                # Chạy tất cả tasks đồng thời
                results = await asyncio.gather(*tasks, return_exceptions=True)
                success_count = sum(r for r in results if isinstance(r, int))

            # Lưu kết quả retry
            self.save_retry_results(total_records, success_count)

            # Merge kết quả với file chính
            if self.crawler.results:
                self.crawler.merge_retry_results(OUTPUT_CONFIG['retry_results_file'])

            self.crawler.logger.info(f"✅ Hoàn thành retry: {success_count}/{total_records} thành công")

            return True

        except Exception as e:
            self.crawler.logger.error(f"❌ Lỗi trong quá trình retry: {e}")
            # Vẫn cố gắng lưu kết quả đã có
            if self.crawler.results:
                self.save_retry_results(len(failed_records), len(self.crawler.results))
            return False
            
    def save_retry_results(self, total_retry, success_count):
        """Lưu kết quả retry vào file riêng"""
        try:
            retry_data = {
                'retry_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_retry_records': total_retry,
                    'successful_retries': success_count,
                    'failed_retries': len(self.crawler.failed_records),
                    'retry_success_rate': f"{(success_count / total_retry * 100):.2f}%" if total_retry > 0 else "0%"
                },
                'results': self.crawler.results,
                'failed_records': self.crawler.failed_records
            }
            
            # Convert Decimal objects to float before saving
            retry_data_converted = self.crawler.convert_decimal_to_float(retry_data)
            
            with open(OUTPUT_CONFIG['retry_results_file'], 'w', encoding='utf-8') as f:
                json.dump(retry_data_converted, f, ensure_ascii=False, indent=2)
                
            self.crawler.logger.info(f"📊 Đã lưu kết quả retry vào: {OUTPUT_CONFIG['retry_results_file']}")
            
        except Exception as e:
            self.crawler.logger.error(f"❌ Lỗi khi lưu retry results: {e}")
            
    def show_retry_summary(self):
        """Hiển thị tóm tắt retry"""
        try:
            with open(OUTPUT_CONFIG['retry_results_file'], 'r', encoding='utf-8') as f:
                retry_data = json.load(f)
                
            info = retry_data['retry_info']
            
            print("\n" + "="*50)
            print("📊 TÓM TẮT KẾT QUẢ RETRY")
            print("="*50)
            print(f"🕐 Thời gian retry: {info['timestamp']}")
            print(f"📋 Tổng bản ghi retry: {info['total_retry_records']}")
            print(f"✅ Thành công: {info['successful_retries']}")
            print(f"❌ Thất bại: {info['failed_retries']}")
            print(f"📈 Tỷ lệ thành công: {info['retry_success_rate']}")
            print("="*50)
            
        except Exception as e:
            self.crawler.logger.error(f"Lỗi khi hiển thị summary: {e}")


async def main():
    """Hàm main async"""
    print("🔄 Bắt đầu retry các bản ghi crawl thất bại (async mode)...")

    retry_handler = RetryFailedRecords()

    try:
        success = await retry_handler.retry_all_failed_records()

        if success:
            retry_handler.show_retry_summary()
            print("\n✅ Hoàn thành retry!")
        else:
            print("\n❌ Retry thất bại!")

    except KeyboardInterrupt:
        print("\n⏹️  Đã dừng retry theo yêu cầu người dùng")
        if retry_handler.crawler.results:
            retry_handler.save_retry_results(0, len(retry_handler.crawler.results))
    except Exception as e:
        print(f"\n❌ Lỗi chương trình: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))