#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ward Crawler Toolkit - Công cụ hoàn chỉnh để crawl dữ liệu geometry xã
Task số 4: crawl dữ liệu geometry của xã, lưu thông tin vào bảng geo_ward
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path

from config import OUTPUT_CONFIG
from crawl_ward_geometry import WardGeometryCrawler
from retry_failed_records import RetryFailedRecords


class WardCrawlerToolkit:
    """Toolkit chính để quản lý crawl ward geometry"""
    
    def __init__(self):
        self.current_session = None
        self.output_dir = Path(OUTPUT_CONFIG['wards_dir'])
        
    def show_banner(self):
        """Hiển thị banner"""
        print("\n" + "="*60)
        print("🏡 WARD CRAWLER TOOLKIT")
        print("Task 4: Crawl dữ liệu geometry của xã")
        print("="*60)
            
    def show_menu(self):
        """Hiển thị menu chính"""
        print("\n🎯 MENU CHÍNH:")
        print("1. 🔄 Crawl dữ liệu ban đầu")
        print("2. 🔁 Retry các bản ghi failed")
        print("3. 🗄️ Đồng bộ geometry vào database")
        print("4. 🏃 Chạy tất cả các task")
        print("5. 📊 Xem báo cáo tổng quan")
        print("0. ❌ Thoát")
        
        
    async def crawl_initial_data(self):
        """Crawl dữ liệu ban đầu (async version)"""
        print("\n🔄 Bắt đầu crawl dữ liệu geometry xã (async mode)...")
        print("=" * 50)

        try:
            crawler = WardGeometryCrawler()
            await crawler.crawl_all_wards()
            self.update_session_info_after_crawl()

            print("\n✅ Hoàn thành crawl dữ liệu ban đầu!")

            # Hỏi có muốn xem báo cáo không
            if input("\n📊 Có muốn xem báo cáo không? (y/n): ").lower() == 'y':
                self.show_report()

        except Exception as e:
            print(f"\n❌ Lỗi khi crawl: {e}")
            
    async def retry_failed_records(self):
        """Retry các bản ghi thất bại (async version)"""
        # Kiểm tra có failed records không
        failed_file = Path(OUTPUT_CONFIG['failed_records_file'])
        if not failed_file.exists():
            print("✅ Không có bản ghi thất bại nào để retry")
            return

        with open(failed_file, 'r', encoding='utf-8') as f:
            failed_data = json.load(f)

        failed_count = len(failed_data.get('failed_records', []))
        if failed_count == 0:
            print("✅ Không có bản ghi thất bại nào để retry")
            return

        print(f"\n🔁 Tìm thấy {failed_count} bản ghi thất bại")
        print("=" * 50)

        # Hỏi có muốn retry không
        if input(f"Có muốn retry {failed_count} bản ghi thất bại không? (y/n): ").lower() != 'y':
            print("⏹️  Đã hủy retry")
            return

        try:
            retry_handler = RetryFailedRecords()
            success = await retry_handler.retry_all_failed_records()

            if success:
                # Cập nhật session info
                self.update_session_info_after_retry()

                retry_handler.show_retry_summary()
                print("\n✅ Hoàn thành retry!")

                # Hỏi có muốn xem báo cáo không
                if input("\n📊 Có muốn xem báo cáo không? (y/n): ").lower() == 'y':
                    self.show_report()
            else:
                print("\n❌ Retry thất bại!")

        except Exception as e:
            print(f"\n❌ Lỗi khi retry: {e}")
            
    def update_session_info_after_crawl(self):
        """Cập nhật thông tin session sau khi crawl"""
        output_path = self.output_dir
            
        try:
            # Đọc kết quả crawl
            results_file = Path(OUTPUT_CONFIG['results_file'])
            if results_file.exists():
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    
                # Cập nhật session info
                info_file = output_path / "session_info.json"
                with open(info_file, 'r', encoding='utf-8') as f:
                    session_info = json.load(f)
                    
                session_info['crawl_info'] = {
                    'total_attempts': results['crawl_info']['total_records'] + results['crawl_info']['failed_records'],
                    'successful_records': results['crawl_info']['total_records'],
                    'failed_records': results['crawl_info']['failed_records'],
                    'last_crawl': datetime.now().isoformat(),
                    'success_rate': results['crawl_info']['success_rate']
                }
                
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(session_info, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"⚠️  Lỗi cập nhật session info: {e}")
            
    def update_session_info_after_retry(self):
        """Cập nhật thông tin session sau khi retry"""
        output_path = self.output_dir
            
        try:
            # Đọc kết quả retry
            retry_file = Path(OUTPUT_CONFIG['retry_results_file'])
            results_file = Path(OUTPUT_CONFIG['results_file'])
            
            if retry_file.exists() and results_file.exists():
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    
                # Cập nhật session info
                info_file = output_path / "session_info.json"
                with open(info_file, 'r', encoding='utf-8') as f:
                    session_info = json.load(f)
                    
                session_info['crawl_info'] = {
                    'total_attempts': results['crawl_info']['total_records'] + results['crawl_info']['failed_records'],
                    'successful_records': results['crawl_info']['total_records'],
                    'failed_records': results['crawl_info']['failed_records'],
                    'last_retry': datetime.now().isoformat(),
                    'success_rate': results['crawl_info']['success_rate']
                }
                
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(session_info, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"⚠️  Lỗi cập nhật session info: {e}")
            
    def show_report(self):
        
        try:
            # Đọc kết quả
            results_file = Path(OUTPUT_CONFIG['results_file'])
            if not results_file.exists():
                print("❌ Chưa có dữ liệu để báo cáo")
                return
                
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
                
            # Hiển thị báo cáo
            print("\n" + "="*60)
            print("📊 BÁO CÁO TỔNG QUAN")
            print("="*60)
            print(f"📅 Thời gian: {results['crawl_info']['timestamp']}")
            print(f"✅ Thành công: {results['crawl_info']['total_records']} bản ghi")
            print(f"❌ Thất bại: {results['crawl_info']['failed_records']} bản ghi")
            print(f"📈 Tỷ lệ thành công: {results['crawl_info']['success_rate']}")
            
            print("="*60)
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo báo cáo: {e}")
            
 
    def sync_geometry_to_database(self):
        """Đồng bộ dữ liệu geometry từ individual files vào database"""
        try:
            print("\n🗄️ ĐỒNG BỘ GEOMETRY VÀO DATABASE")
            print("="*50)

            # Kiểm tra thư mục individual files
            individual_dir = Path(OUTPUT_CONFIG['wards_dir'])
            if not individual_dir.exists():
                print(f"❌ Không tìm thấy thư mục {individual_dir}")
                print("💡 Hãy chạy crawl dữ liệu trước")
                return

            # Lấy danh sách files
            json_files = list(individual_dir.glob("ward_*.json"))
            if not json_files:
                print("❌ Không tìm thấy file nào trong thư mục individual")
                return

            print(f"📁 Tìm thấy {len(json_files)} files individual")

            # Xác nhận từ user
            confirm = input(f"\n⚠️ Bạn có muốn đồng bộ {len(json_files)} records vào database? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ Hủy bỏ đồng bộ")
                return

            # Import database sync class
            from database_sync import DatabaseGeometrySync

            # Tạo instance và chạy sync
            sync = DatabaseGeometrySync()
            sync.sync_all_geometry_files(individual_dir)

        except Exception as e:
            print(f"\n❌ Lỗi khi đồng bộ geometry: {e}")

    async def run_all_tasks(self):
        """Chạy tất cả các task (async version)"""
        await self.crawl_initial_data()
        await self.retry_failed_records()
        self.sync_geometry_to_database()


    async def run(self):
        """Chạy toolkit (async version)"""

        while True:
            try:
                self.show_banner()
                self.show_menu()

                choice = input("\nNhập lựa chọn: ").strip()

                if choice == '1':
                    await self.crawl_initial_data()
                elif choice == '2':
                    await self.retry_failed_records()
                elif choice == '3':
                    self.sync_geometry_to_database()
                elif choice == '4':
                    await self.run_all_tasks()
                elif choice == '5':
                    self.show_report()
                elif choice == '0':
                    print("👋 Bye!")
                    break
                else:
                    print("❌ Lựa chọn không hợp lệ")

            except KeyboardInterrupt:
                print("\n\n👋 Bye!")
                break
            except Exception as e:
                print(f"\n❌ Lỗi: {e}")
                
                
async def main():
    """Hàm main async"""
    toolkit = WardCrawlerToolkit()
    await toolkit.run()


if __name__ == "__main__":
    asyncio.run(main())