#!/usr/bin/env python3
"""
VNSDI Toolkit - Tool hoàn chỉnh cho việc crawl dữ liệu VNSDI
Tích hợp: Crawl → Retry → Export CSV → Create JIRA Task
"""

import sys
from pathlib import Path
import subprocess
from crawl_from_province_file import main as crawl_from_province_file

class VNSDIToolkit:
    def __init__(self):
        self.crawled_data_path = Path("crawled_data")
        self.setup_directories()
        
    def setup_directories(self):
        """Tạo các thư mục cần thiết"""
        self.crawled_data_path.mkdir(exist_ok=True)
        
    def print_header(self):
        """In header của tool"""
        print("=" * 70)
        print("🚀 VNSDI TOOLKIT - CÔNG CỤ CRAWL DỮ LIỆU VNSDI HOÀN CHỈNH")
        print("=" * 70)
        print("📍 Crawl dữ liệu đơn vị hành chính Việt Nam từ VNSDI")
        print("🔄 Tích hợp: Crawl → Retry → Export CSV → Create JIRA Task")
        print("=" * 70)
        
    def print_menu(self):
        """In menu chính"""
        print("\n🎯 MENU CHÍNH:")
        print("1. 🔄 Crawl dữ liệu ban đầu")
        print("2. 🔁 Retry các tỉnh failed")
        print("3. 📊 Export dữ liệu to CSV")
        print("4. � Chạy workflow hoàn chỉnh")
        print("0. ❌ Thoát")
        print("-" * 50)
  
    def crawl_initial_data(self):
        """Crawl dữ liệu ban đầu"""
        print("\n🔄 BẮT ĐẦU CRAWL DỮ LIỆU BAN ĐẦU")
        print("=" * 50)
        
        # Chạy crawl_from_province_file.py
        try:
            crawl_from_province_file()
            print("✅ Crawl dữ liệu ban đầu thành công!")
        except Exception as e:
            print(f"❌ Lỗi chạy crawl: {e}")
            
    def retry_failed_provinces(self):
        """Retry các tỉnh failed"""
        print("\n🔁 BẮT ĐẦU RETRY CÁC TỈNH FAILED")
        print("=" * 50)
        
        # Import và sử dụng VNSDIRetryTool
        from retry_failed_provinces import VNSDIRetryTool
        
        try:
            retry_tool = VNSDIRetryTool()
            retry_summary = retry_tool.run_retry()
            if retry_summary:
                print(f"\n✅ Retry hoàn thành!")
                print(f"📊 Kết quả: {retry_summary['success_count']}/{retry_summary['total_retries']} thành công")
                
                # Tự động cập nhật crawl_results.json (đã tích hợp trong retry_tool)
                print("📝 Crawl results đã được cập nhật tự động!")              
                self.export_to_csv()
                    
        except Exception as e:
            print(f"❌ Lỗi retry: {e}")
            
    def export_to_csv(self):
        """Export dữ liệu to CSV"""
        print("\n📊 BẮT ĐẦU EXPORT DỮ LIỆU TO CSV")
        print("=" * 50)
        
        
        print("🎯 Chọn loại export:")
        print("1. Export tổng hợp (tất cả tỉnh trong 1 file CSV)")
        print("2. Export thống kê (thông tin tổng quan từng tỉnh)")
        print("3. Export theo tỉnh (mỗi tỉnh 1 file CSV)")
        print("4. Export tất cả (cả 3 loại trên)")
        
        try:
            choice = input("\nNhập lựa chọn (1-4): ").strip()
            
            # Chạy export_to_csv.py với input tự động
            process = subprocess.Popen([sys.executable, "export_to_csv.py"], 
                                     stdin=subprocess.PIPE, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, 
                                     text=True)
            
            stdout, stderr = process.communicate(input=choice)
            
            if process.returncode == 0:
                print("✅ Export CSV thành công!")
                print(stdout)
                
            else:
                print(f"❌ Lỗi export: {stderr}")
                
        except Exception as e:
            print(f"❌ Lỗi chạy export: {e}")
            
    def run_complete_workflow(self):
        """Chạy workflow hoàn chỉnh"""
        print("\n🚀 BẮT ĐẦU WORKFLOW HOÀN CHỈNH")
        print("=" * 50)
        
        # 1. Crawl ban đầu
        print("🔄 Bước 1: Crawl dữ liệu ban đầu...")
        self.crawl_initial_data()
        
        # 2. Retry tự động
        print("\n🔁 Bước 2: Retry các tỉnh failed...")
        self.retry_failed_provinces()
        
        # 3. Export CSV
        print("\n📊 Bước 3: Export dữ liệu to CSV...")
        # Tự động chọn export tất cả
        process = subprocess.Popen([sys.executable, "export_to_csv.py"], 
                                 stdin=subprocess.PIPE, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, 
                                 text=True)
        
        stdout, stderr = process.communicate(input="4")  # Export tất cả
        
        if process.returncode == 0:
            print("✅ Export CSV thành công!")
        else:
            print(f"❌ Lỗi export: {stderr}")
       
         # 4. Đồng bộ dữ liệu vào database
        print("\n🔄 Bước 4: Đồng bộ dữ liệu vào database...")
        self.sycn_to_database()
        
         
        print("\n🎉 WORKFLOW HOÀN CHỈNH THÀNH CÔNG!")
        
    def sycn_to_database(self):
        """Đồng bộ dữ liệu vào database"""
        print("\n🔄 BẮT ĐẦU ĐỒNG BỘ DỮ LIỆU VÀO DATABASE")
        print("=" * 50)
        
        
    def run(self):
        """Chạy tool chính"""
        self.print_header()
        
        while True:
            self.print_menu()
            
            try:
                choice = input("Nhập lựa chọn (0-4): ").strip()
                
                if choice == "0":
                    print("👋 Tạm biệt!")
                    break
                elif choice == "1":
                    self.crawl_initial_data()
                elif choice == "2":
                    self.retry_failed_provinces()
                elif choice == "3":
                    self.export_to_csv()
                elif choice == "4":
                    self.sycn_to_database()
              
                elif choice == "5":
                    # Hidden option: chạy workflow hoàn chỉnh
                    self.run_complete_workflow()
                else:
                    print("❌ Lựa chọn không hợp lệ!")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Tạm biệt!")
                break
            except Exception as e:
                print(f"❌ Lỗi: {e}")
                
            # Pause trước khi hiển thị menu lại
            if choice != "0":
                input("\nNhấn Enter để tiếp tục...")

def main():
    """Main function"""
    toolkit = VNSDIToolkit()
    toolkit.run()

if __name__ == "__main__":
    main() 