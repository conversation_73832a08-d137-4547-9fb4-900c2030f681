#!/usr/bin/env python3
"""
Tool retry để crawl lại các provinces bị failed
"""

import json
import requests
import time
from pathlib import Path
from datetime import datetime
import logging

class VNSDIRetryTool:
    def __init__(self):
        self.base_url = "https://vnsdi.mae.gov.vn/basemap/rest/services/34DVHC/MapServer/0/query"
        self.token = "ExStvSdFXfM9AqU46uMerpamG7Snw7xZmlRf8VsRpynP4ggdhKW7O5lIcaaa7PcGl56gL1vYn4HYuSAi06zhMA.."
        
        # Headers đầy đủ từ crawler gốc (loại bỏ if-none-match để tránh cache)
        self.headers = {
            'accept': '*/*',
            'accept-language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'priority': 'u=1, i',
            'referer': 'https://vnsdi.mae.gov.vn/bandonen/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Cookie': 'AGS_ROLES="sym-gcm|1|y64cqvJ9gXTzM9iRxlEz9g==|dxbHs9n4KABMcj8LE6FuNLTDEnfl1QxnCoiRQbEeJWlqc5enfYSA6k-qoEz_VIACUb-y"; AGS_ROLES="sym-gcm|1|gr-RVE9eQ6G1FZnSphuFxQ==|l3f6bYO1N68Ytj1MxeTnG6xQ3ihHz777mTa3laqQl0jjfryfB1fSsQUSgTI-B8SAZ_iU"',
            'cache-control': 'no-cache'
        }
        
        self.retry_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.retry_dir = Path('crawled_data')
        self.retry_dir.mkdir(parents=True, exist_ok=True)
        
        # Tạo thư mục con
        (self.retry_dir / "provinces").mkdir(exist_ok=True)
        (self.retry_dir / "logs").mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.retry_dir / "logs" / "retry.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_province_data(self):
        """Load danh sách provinces từ geometry_province.json"""
        try:
            with open('geometry_province.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Lỗi đọc file geometry_province.json: {e}")
            return None
    
    def find_failed_provinces(self):
        """Tìm các provinces bị failed từ crawl session trước"""
        failed_provinces = []
        
        # Kiểm tra results từ session gốc
        results_file = self.retry_dir / "provinces/crawl_results.json"
        if results_file.exists():
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
                failed_provinces.extend([
                    p for p in results.get("results", [])
                    if p.get("status") == "error"
                ])
        
        # Kiểm tra results từ retry sessions trước
        retry_dirs = [d for d in self.retry_dir.iterdir() 
                      if d.is_dir() and d.name.startswith("retry_")]
        
        for retry_dir in retry_dirs:
            retry_file = retry_dir / "provinces/retry_results.json"
            if retry_file.exists():
                with open(retry_file, 'r', encoding='utf-8') as f:
                    retry_results = json.load(f)
                    failed_provinces.extend([
                        p for p in retry_results.get("results", [])
                        if p.get("status") == "error"
                    ])
        
        return failed_provinces
    
    def _generate_curl_command(self, params):
        """Tạo curl command từ params với headers đầy đủ"""
        import urllib.parse
        
        # Encode params
        query_string = urllib.parse.urlencode(params)
        full_url = f"{self.base_url}?{query_string}"
        
        # Tạo curl command với tất cả headers
        curl_command = f'''curl -X GET "{full_url}" \\'''
        
        # Thêm tất cả headers
        for key, value in self.headers.items():
            curl_command += f'\n  -H "{key}: {value}" \\'
        
        # Thêm timeout
        curl_command += '\n  --connect-timeout 30'
        
        return curl_command
    
    def crawl_province(self, province_code, province_name):
        """Crawl dữ liệu cho một province"""
        try:
            params = {
                'returnGeometry': 'true',
                'where': f"MATINH='{province_code}'",
                'outSR': '4326',
                'outFields': '*',
                'token': self.token,
                'f': 'json'
            }
            
            # Tạo curl command để log
            curl_command = self._generate_curl_command(params)
            
            self.logger.info(f"Đang crawl {province_name} (Code: {province_code})...")
            self.logger.info(f"🔗 Curl command: {curl_command}")
            
            response = requests.get(
                self.base_url, 
                params=params, 
                timeout=30,
                headers=self.headers
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Kiểm tra lỗi token
                    if 'error' in data:
                        error_code = data['error'].get('code', 'unknown')
                        error_message = data['error'].get('message', 'unknown')
                        
                        if error_code == 499 and 'token' in error_message.lower():
                            self.logger.error(f"🔑 API yêu cầu token: {province_name}")
                            self.logger.error(f"   Lỗi: {error_message}")
                            self.logger.error(f"🔍 Kiểm tra thủ công với curl:")
                            self.logger.error(f"   {curl_command}")
                            return {
                                "status": "token_required",
                                "error": f"Token required: {error_message}",
                                "curl_command": curl_command
                            }
                        else:
                            self.logger.error(f"🚫 API Error: {province_name} - {error_message}")
                            self.logger.error(f"🔍 Kiểm tra thủ công với curl:")
                            self.logger.error(f"   {curl_command}")
                            return {
                                "status": "api_error",
                                "error": f"API Error {error_code}: {error_message}",
                                "curl_command": curl_command
                            }
                    
                    if 'features' in data and data['features']:
                        features_count = len(data['features'])
                        
                        # Lưu dữ liệu
                        filename = f"province_{province_code}_{province_name.replace(' ', '_')}.json"
                        filepath = self.retry_dir / "provinces" / filename
                        
                        with open(filepath, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        self.logger.info(f"✓ Thành công: {province_name} - {features_count} features")
                        return {
                            "status": "success",
                            "features_count": features_count,
                            "file_path": str(filepath)
                        }
                    else:
                        self.logger.warning(f"✗ Không có dữ liệu: {province_name}")
                        self.logger.warning(f"🔍 Kiểm tra thủ công với curl:")
                        self.logger.warning(f"   {curl_command}")
                        return {
                            "status": "no_data",
                            "error": "No features in response",
                            "curl_command": curl_command
                        }
                        
                except json.JSONDecodeError as e:
                    self.logger.error(f"✗ Lỗi JSON: {province_name} - {e}")
                    self.logger.error(f"🔍 Kiểm tra thủ công với curl:")
                    self.logger.error(f"   {curl_command}")
                    return {
                        "status": "still_failed",
                        "error": str(e),
                        "curl_command": curl_command
                    }
            else:
                if response.status_code == 304:
                    # HTTP 304 Not Modified - có thể data đã có từ cache
                    self.logger.warning(f"⚠️ HTTP 304 Not Modified: {province_name}")
                    self.logger.warning(f"   Dữ liệu có thể đã tồn tại trong cache server")
                    self.logger.warning(f"🔍 Kiểm tra thủ công với curl:")
                    self.logger.warning(f"   {curl_command}")
                    return {
                        "status": "cache_not_modified",
                        "error": "HTTP 304 Not Modified",
                        "curl_command": curl_command
                    }
                else:
                    self.logger.error(f"✗ HTTP Error: {province_name} - Status {response.status_code}")
                    self.logger.error(f"🔍 Kiểm tra thủ công với curl:")
                    self.logger.error(f"   {curl_command}")
                    return {
                        "status": "still_failed",
                        "error": f"HTTP {response.status_code}",
                        "curl_command": curl_command
                    }
                
        except Exception as e:
            self.logger.error(f"✗ Exception: {province_name} - {e}")
            curl_command = self._generate_curl_command({
                'returnGeometry': 'true',
                'where': f"MATINH='{province_code}'",
                'outSR': '4326',
                'outFields': '*',
                'token': self.token,
                'f': 'json'
            })
            self.logger.error(f"🔍 Kiểm tra thủ công với curl:")
            self.logger.error(f"   {curl_command}")
            return {
                "status": "still_failed",
                "error": str(e),
                "curl_command": curl_command
            }
    
    def run_retry(self):
        """Chạy retry cho tất cả provinces bị failed"""
        self.logger.info("=== BẮT ĐẦU RETRY FAILED PROVINCES ===")
        
        # Load province data
        province_data = self.load_province_data()
        if not province_data:
            return
        
        # Tạo province lookup
        province_lookup = {
            str(p['attributes']['MATINH']): p['attributes']['TENTINH']
            for p in province_data['features']
        }
        
        # Tìm failed provinces
        failed_provinces = self.find_failed_provinces()
        
        if not failed_provinces:
            self.logger.info("Không có provinces nào bị failed cần retry")
            return
        
        self.logger.info(f"Tìm thấy {len(failed_provinces)} provinces cần retry")
        
        retry_results = []
        success_count = 0
        
        for province in failed_provinces:
            province_code = province.get('province_code')
            province_name = province_lookup.get(province_code, province.get('province_name', 'Unknown'))
            previous_error = province.get('error', province.get('current_error', 'Unknown'))
            
            # Retry crawl
            result = self.crawl_province(province_code, province_name)
            
            # Thêm thông tin bổ sung
            result.update({
                'province_code': province_code,
                'province_name': province_name,
                'previous_error': previous_error
            })
            
            if result['status'] == 'success':
                success_count += 1
            
            retry_results.append(result)
            
            # Delay giữa các requests
            time.sleep(1)
        
        # Lưu kết quả
        retry_summary = {
            'timestamp': datetime.now().isoformat(),
            'base_session': self.retry_dir.name,
            'total_retries': len(retry_results),
            'success_count': success_count,
            'still_failed_count': len(retry_results) - success_count,
            'results': retry_results
        }
        
        with open(self.retry_dir / "provinces/retry_results.json", 'w', encoding='utf-8') as f:
            json.dump(retry_summary, f, ensure_ascii=False, indent=2)
        
        # Log kết quả
        self.logger.info("=== KẾT QUẢ RETRY ===")
        self.logger.info(f"Tổng số retry: {len(retry_results)}")
        self.logger.info(f"Thành công: {success_count}")
        self.logger.info(f"Vẫn failed: {len(retry_results) - success_count}")
        
        for result in retry_results:
            status_icon = "✓" if result['status'] == 'success' else "✗"
            self.logger.info(f"{status_icon} {result['province_name']} ({result['province_code']})")
        
        # Lưu curl commands cho các failed provinces
        failed_curls = [r for r in retry_results if r['status'] != 'success' and 'curl_command' in r]
        if failed_curls:
            curl_file = self.retry_dir / "failed_curl_commands.txt"
            with open(curl_file, 'w', encoding='utf-8') as f:
                f.write("# CURL COMMANDS ĐỂ KIỂM TRA THỦ CÔNG\n")
                f.write("# Tạo từ retry session\n\n")
                for result in failed_curls:
                    f.write(f"# {result['province_name']} (Code: {result['province_code']})\n")
                    f.write(f"# Lỗi: {result['error']}\n")
                    f.write(f"{result['curl_command']}\n\n")
            self.logger.info(f"📋 Curl commands lưu tại: {curl_file}")
        
        self.logger.info(f"Kết quả lưu tại: {self.retry_dir}")
        
        # Cập nhật crawl_results.json chính
        self.update_main_crawl_results(retry_summary)
        
        return retry_summary
    
    def update_main_crawl_results(self, retry_summary):
        """Cập nhật crawl_results.json chính với kết quả retry"""
        try:
            crawl_results_file = self.retry_dir / "provinces/crawl_results.json"
            
            # Đọc crawl_results.json hiện tại
            if not crawl_results_file.exists():
                self.logger.warning("Không tìm thấy file crawl_results.json")
                return
            
            with open(crawl_results_file, 'r', encoding='utf-8') as f:
                crawl_results = json.load(f)
            
            # Tạo dictionary để lookup nhanh
            province_lookup = {
                result['province_code']: result 
                for result in crawl_results['results']
            }
            
            # Cập nhật các province từ retry
            updated_count = 0
            for retry_result in retry_summary['results']:
                province_code = retry_result['province_code']
                
                if province_code in province_lookup:
                    if retry_result['status'] == 'success':
                        # Cập nhật thành công
                        province_lookup[province_code].update({
                            'status': 'success',
                            'features_count': retry_result['features_count'],
                            'file_path': retry_result['file_path']
                        })
                        # Xóa error field nếu có
                        if 'error' in province_lookup[province_code]:
                            del province_lookup[province_code]['error']
                        updated_count += 1
                        self.logger.info(f"✓ Cập nhật {retry_result['province_name']} thành công")
                    else:
                        # Cập nhật lỗi mới
                        province_lookup[province_code].update({
                            'status': 'error',
                            'error': retry_result['error'],
                            'features_count': 0
                        })
                        self.logger.info(f"✗ Cập nhật {retry_result['province_name']} vẫn lỗi")
            
            # Tính lại success_count và error_count
            success_count = sum(1 for result in crawl_results['results'] if result['status'] == 'success')
            error_count = sum(1 for result in crawl_results['results'] if result['status'] == 'error')
            
            # Cập nhật timestamp và counts
            crawl_results.update({
                'timestamp': datetime.now().isoformat(),
                'success_count': success_count,
                'error_count': error_count,
                'last_retry': retry_summary['timestamp']
            })
            
            # Lưu lại file
            with open(crawl_results_file, 'w', encoding='utf-8') as f:
                json.dump(crawl_results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📊 Đã cập nhật crawl_results.json:")
            self.logger.info(f"   - Provinces updated: {updated_count}")
            self.logger.info(f"   - Success: {success_count}/{crawl_results['total_provinces']}")
            self.logger.info(f"   - Error: {error_count}/{crawl_results['total_provinces']}")
            
        except Exception as e:
            self.logger.error(f"Lỗi cập nhật crawl_results.json: {e}")

def main():
    """Main function"""
    print("=== VNSDI RETRY TOOL ===")
    
    # Tìm session gần nhất
    crawled_data_path = Path("crawled_data")
    if not crawled_data_path.exists():
        print("Không tìm thấy thư mục crawled_data")
        return
    
    
    # Chạy retry
    retry_tool = VNSDIRetryTool()
    retry_tool.run_retry()

if __name__ == "__main__":
    main() 