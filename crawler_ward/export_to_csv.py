#!/usr/bin/env python3
"""
Export dữ liệu VNS<PERSON> từ JSON sang CSV format
"""

import json
import csv
from pathlib import Path
from datetime import datetime
import os

def tim_session_moi_nhat():
    """
    Tìm session crawl mới nhất
    """
    crawl_dir = Path("crawled_data")
    if not crawl_dir.exists():
        return None
    
    sessions = [d for d in crawl_dir.iterdir() if d.is_dir() and d.name.startswith("crawl_session_")]
    if not sessions:
        return None
    
    return sorted(sessions)[-1]

def export_tong_hop_csv(session_dir):
    """
    Export tổng hợp dữ liệu tất cả tỉnh vào một file CSV
    """
    provinces_dir = session_dir / "provinces"
    if not provinces_dir.exists():
        print("❌ Không tìm thấy thư mục provinces")
        return
    
    # Tạo thư mục exports
    exports_dir = session_dir / "exports"
    exports_dir.mkdir(exist_ok=True)
    
    # File CSV tổng hợp
    csv_file = exports_dir / "vnsdi_data_full.csv"
    
    all_records = []
    
    print("🔄 Đang đọc dữ liệu từ các file JSON...")
    
    for json_file in provinces_dir.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'features' in data:
                for feature in data['features']:
                    attributes = feature.get('attributes', {})
                    geometry = feature.get('geometry', {})
                    
                    # Tạo record cho CSV
                    record = {
                        'OBJECTID': attributes.get('OBJECTID', ''),
                        'MA_TINH': attributes.get('MATINH', ''),
                        'TEN_TINH': attributes.get('TENTINH', ''),
                        'MA_XA': attributes.get('MAXA', ''),
                        'TEN_XA': attributes.get('TENXA', ''),
                        'DIEN_TICH_KM2': attributes.get('DIENTICH', ''),
                        'DAN_SO': attributes.get('DANSO', ''),
                        'NGAY_HIEU_LUC': attributes.get('NGAYHIEULUC', ''),
                        'CAN_CU_PHAP_LY': attributes.get('CANCUPHAPLY', ''),
                        'NGAY_XUAT_BAN': attributes.get('NGAYXUATBAN', ''),
                        'KINH_DO': geometry.get('x', ''),
                        'VI_DO': geometry.get('y', ''),
                        'SOURCE_FILE': json_file.name
                    }
                    
                    all_records.append(record)
        
        except Exception as e:
            print(f"⚠️ Lỗi đọc file {json_file.name}: {e}")
    
    # Ghi vào CSV
    if all_records:
        fieldnames = [
            'OBJECTID', 'MA_TINH', 'TEN_TINH', 'MA_XA', 'TEN_XA',
            'DIEN_TICH_KM2', 'DAN_SO', 'NGAY_HIEU_LUC', 'CAN_CU_PHAP_LY',
            'NGAY_XUAT_BAN', 'KINH_DO', 'VI_DO', 'SOURCE_FILE'
        ]
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_records)
        
        print(f"✅ Đã export {len(all_records)} records vào {csv_file}")
        return csv_file
    else:
        print("❌ Không có dữ liệu để export")
        return None

def export_thong_ke_csv(session_dir):
    """
    Export thống kê theo từng tỉnh
    """
    results_file = session_dir / "provinces/crawl_results.json"
    if not results_file.exists():
        print("❌ Không tìm thấy file crawl_results.json")
        return
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    exports_dir = session_dir / "exports"
    exports_dir.mkdir(exist_ok=True)
    
    # Tạo dữ liệu thống kê
    stats_records = []
    
    for result in results['results']:
        record = {
            'MA_TINH': result['province_code'],
            'TEN_TINH': result['province_name'],
            'TRANG_THAI': 'Thành công' if result['status'] == 'success' else 'Thất bại',
            'SO_DON_VI_HANH_CHINH': result.get('features_count', 0),
            'LOI': result.get('error', '') if result['status'] == 'error' else '',
            'FILE_OUTPUT': result.get('file_path', '').split('/')[-1] if result.get('file_path') else ''
        }
        stats_records.append(record)
    

def export_theo_tinh_csv(session_dir):
    """
    Export từng tỉnh thành file CSV riêng biệt
    """
    provinces_dir = session_dir / "provinces"
    if not provinces_dir.exists():
        print("❌ Không tìm thấy thư mục provinces")
        return
    
    exports_dir = session_dir / "exports" / "by_province"
    exports_dir.mkdir(parents=True, exist_ok=True)
    
    exported_files = []
    
    print("🔄 Đang export từng tỉnh...")
    
    for json_file in provinces_dir.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'features' in data:
                # Tạo CSV file cho tỉnh này
                csv_filename = json_file.stem + ".csv"
                csv_file = exports_dir / csv_filename
                
                records = []
                
                for feature in data['features']:
                    attributes = feature.get('attributes', {})
                    geometry = feature.get('geometry', {})
                    
                    record = {
                        'OBJECTID': attributes.get('OBJECTID', ''),
                        'MA_TINH': attributes.get('MATINH', ''),
                        'TEN_TINH': attributes.get('TENTINH', ''),
                        'MA_XA': attributes.get('MAXA', ''),
                        'TEN_XA': attributes.get('TENXA', ''),
                        'DIEN_TICH_KM2': attributes.get('DIENTICH', ''),
                        'DAN_SO': attributes.get('DANSO', ''),
                        'NGAY_HIEU_LUC': attributes.get('NGAYHIEULUC', ''),
                        'CAN_CU_PHAP_LY': attributes.get('CANCUPHAPLY', ''),
                        'NGAY_XUAT_BAN': attributes.get('NGAYXUATBAN', ''),
                        'KINH_DO': geometry.get('x', ''),
                        'VI_DO': geometry.get('y', '')
                    }
                    
                    records.append(record)
                
                # Ghi vào CSV
                if records:
                    fieldnames = [
                        'OBJECTID', 'MA_TINH', 'TEN_TINH', 'MA_XA', 'TEN_XA',
                        'DIEN_TICH_KM2', 'DAN_SO', 'NGAY_HIEU_LUC', 'CAN_CU_PHAP_LY',
                        'NGAY_XUAT_BAN', 'KINH_DO', 'VI_DO'
                    ]
                    
                    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(records)
                    
                    exported_files.append(csv_file)
                    print(f"  ✅ {json_file.name} -> {csv_filename} ({len(records)} records)")
        
        except Exception as e:
            print(f"⚠️ Lỗi export {json_file.name}: {e}")
    
    print(f"✅ Đã export {len(exported_files)} file CSV theo tỉnh")
    return exported_files

def main():
    """
    Main function
    """
    print("=" * 60)
    print("VNSDI DATA EXPORTER - JSON TO CSV")
    print("=" * 60)
    
    
    
    # Hỏi người dùng muốn export gì
    print("\n🎯 Chọn loại export:")
    print("1. Export tổng hợp (tất cả tỉnh trong 1 file CSV)")
    print("2. Export thống kê (thông tin tổng quan từng tỉnh)")
    print("3. Export theo tỉnh (mỗi tỉnh 1 file CSV)")
    print("4. Export tất cả (cả 3 loại trên)")
    
    choice = input("\nNhập lựa chọn (1-4): ").strip()
    session_dir = Path("crawled_data")
    if choice == "1":
        export_tong_hop_csv(session_dir)
    elif choice == "2":
        export_thong_ke_csv(session_dir)
    elif choice == "3":
        export_theo_tinh_csv(session_dir)
    elif choice == "4":
        print("\n🚀 Đang export tất cả...")
        export_tong_hop_csv(session_dir)
        export_thong_ke_csv(session_dir)
        export_theo_tinh_csv(session_dir)
    else:
        print("❌ Lựa chọn không hợp lệ")
        return
    
    # Thông báo kết quả
    exports_dir = session_dir / "exports"
    if exports_dir.exists():
        print(f"\n📂 Các file CSV đã được lưu trong: {exports_dir}")
        
        # Liệt kê các file đã tạo
        csv_files = list(exports_dir.glob("**/*.csv"))
        if csv_files:
            print(f"\n📋 Danh sách file CSV ({len(csv_files)}):")
            for csv_file in csv_files:
                size_mb = round(csv_file.stat().st_size / (1024 * 1024), 2)
                print(f"  📄 {csv_file.name} ({size_mb} MB)")
    
    print(f"\n🎉 Export hoàn thành!")

if __name__ == "__main__":
    main() 