# 🚀 VNSDI TOOLKIT - HƯỚNG DẪN SỬ DỤNG

## 📋 Tổng quan

**VNSDI Toolkit** là công cụ hoàn chỉnh để crawl dữ liệu đơn vị hành chính Việt Nam từ VNSDI (Vietnam National Spatial Data Infrastructure).

### 🎯 Chức năng chính:
1. **Crawl dữ liệu ban đầu** - Crawl tất cả 34 tỉnh/thành phố
2. **Retry các tỉnh failed** - Tự động retry và cập nhật kết quả
3. **Export dữ liệu to CSV** - Xuất ra nhiều định dạng CSV
4. **Tạo JIRA task** - Tự động tạo task báo cáo
5. **Báo cáo tổng quan** - Thống kê chi tiết

---

## 🚀 Cách sử dụng

### Khởi chạy tool:
```bash
python3 vnsdi_toolkit.py
```

### <PERSON><PERSON> ch<PERSON>h:
```
🎯 MENU CHÍNH:
1. 🔄 Crawl dữ liệu ban đầu
2. 🔁 Retry các tỉnh failed
3. 📊 Export dữ liệu to CSV
4. 📋 Tạo JIRA task
5. 📈 Xem báo cáo tổng quan
6. 🔧 Chọn session làm việc
0. ❌ Thoát
```

---

## 📖 Hướng dẫn từng chức năng

### 1. 🔄 Crawl dữ liệu ban đầu

**Mục đích:** Crawl dữ liệu từ tất cả 34 tỉnh/thành phố Việt Nam

**Cách sử dụng:**
- Chọn option `1` từ menu
- Tool sẽ tự động crawl từ file `geometry_province.json`
- Sau khi crawl xong, tool sẽ hỏi có muốn retry các tỉnh failed không

**Kết quả:**
- Tạo session mới với timestamp (VD: `crawl_session_20250716_105849`)
- Lưu dữ liệu JSON cho từng tỉnh thành công
- Tạo file `crawl_results.json` với tổng kết

### 2. 🔁 Retry các tỉnh failed

**Mục đích:** Retry lại các tỉnh bị lỗi và tự động cập nhật kết quả

**Cách sử dụng:**
- Chọn option `2` từ menu
- Tool sẽ tự động tìm các tỉnh failed và retry
- **Tự động cập nhật `crawl_results.json`** sau khi retry

**Tính năng đặc biệt:**
- ✅ **Tích hợp update result** - Không cần chạy update thủ công
- ✅ **Curl command logging** - Lưu curl commands để test thủ công
- ✅ **Token và headers đầy đủ** - Giống như crawler gốc
- ✅ **Xử lý HTTP 304 cache** - Tự động bypass cache

**Kết quả:**
- Cập nhật `crawl_results.json` với kết quả mới
- Lưu file `retry_results.json` cho audit trail
- Tạo `failed_curl_commands.txt` nếu có lỗi

### 3. 📊 Export dữ liệu to CSV

**Mục đích:** Xuất dữ liệu ra nhiều định dạng CSV

**Các loại export:**
1. **Export tổng hợp** - Tất cả tỉnh trong 1 file CSV
2. **Export thống kê** - Thông tin tổng quan từng tỉnh
3. **Export theo tỉnh** - Mỗi tỉnh 1 file CSV
4. **Export tất cả** - Cả 3 loại trên

**Cách sử dụng:**
- Chọn option `3` từ menu
- Chọn loại export mong muốn (1-4)
- Tool sẽ tự động export và hiển thị kết quả

### 4. 📋 Tạo JIRA task

**Mục đích:** Tự động tạo task JIRA báo cáo tiếng Việt

**Cách sử dụng:**
- Chọn option `4` từ menu
- Tool sẽ tự động tạo file JIRA task dựa trên dữ liệu hiện tại

**Kết quả:**
- Tạo file `jira_task_vietnamese.md` với nội dung tiếng Việt
- Bao gồm thống kê, danh sách tỉnh thành công/thất bại
- Định dạng Markdown cho JIRA

### 5. 📈 Xem báo cáo tổng quan

**Mục đích:** Hiển thị thống kê chi tiết về session hiện tại

**Thông tin hiển thị:**
- Số lượng tỉnh/thành thành công vs thất bại
- Tỷ lệ thành công
- Tổng số đơn vị hành chính
- Danh sách tỉnh thất bại (nếu có)
- Top 5 tỉnh có nhiều đơn vị nhất
- Thông tin session và thời gian

### 6. 🔧 Chọn session làm việc

**Mục đích:** Chuyển đổi giữa các session khác nhau

**Cách sử dụng:**
- Chọn option `6` từ menu
- Chọn session từ danh sách
- Tool sẽ chuyển sang session được chọn

---

## 🌟 Tính năng nâng cao

### Workflow hoàn chỉnh tự động

**Cách kích hoạt:** Nhập `auto` trong menu chính

**Quá trình tự động:**
1. Crawl dữ liệu ban đầu
2. Retry các tỉnh failed
3. Export tất cả định dạng CSV
4. Tạo JIRA task
5. Hiển thị báo cáo tổng kết

---

## 📂 Cấu trúc thư mục

```
sapnhap/
├── vnsdi_toolkit.py              # Tool chính
├── crawl_from_province_file.py   # Crawl module
├── retry_failed_provinces.py     # Retry module (có tích hợp update)
├── export_to_csv.py              # Export module
├── jira_task_vietnamese.py       # JIRA task module
├── geometry_province.json        # Dữ liệu input
├── requirements.txt              # Dependencies
└── crawled_data/                 # Thư mục output
    └── crawl_session_YYYYMMDD_HHMMSS/
        ├── provinces/            # JSON files cho từng tỉnh
        ├── exports/              # CSV files
        ├── logs/                 # Log files
        ├── crawl_results.json    # Kết quả chính
        ├── retry_results.json    # Kết quả retry
        └── failed_curl_commands.txt  # Curl commands
```

---

## 🔧 Cài đặt và Dependencies

### Requirements:
```bash
pip install requests
```

### Files cần thiết:
- `geometry_province.json` - Dữ liệu 34 tỉnh/thành
- `crawl_vnsdi_data.py` - Crawler class
- `retry_failed_provinces.py` - Retry tool
- `export_to_csv.py` - Export tool
- `jira_task_vietnamese.py` - JIRA tool

---

## 🎯 Workflow khuyến nghị

### Lần đầu sử dụng:
1. Chạy `python3 vnsdi_toolkit.py`
2. Chọn `1` để crawl dữ liệu ban đầu
3. Chọn `y` để retry các tỉnh failed
4. Chọn `y` để export CSV
5. Chọn `y` để tạo JIRA task

### Sử dụng hàng ngày:
1. Chạy `python3 vnsdi_toolkit.py`
2. Chọn `5` để xem báo cáo
3. Chọn `2` để retry nếu cần
4. Chọn `3` để export dữ liệu mới

---

## 🔍 Troubleshooting

### Lỗi token required:
- Tool đã tích hợp token và headers đầy đủ
- Kiểm tra file `failed_curl_commands.txt` để test thủ công

### Lỗi HTTP 304:
- Tool đã tự động xử lý cache với `cache-control: no-cache`
- Loại bỏ `if-none-match` header

### Session không tìm thấy:
- Chạy crawl dữ liệu ban đầu để tạo session mới
- Hoặc chọn session khác từ menu

---

## 📊 Kết quả mong đợi

### Tỷ lệ thành công: **100%** (34/34 tỉnh)
### Tổng số đơn vị hành chính: **~3,300 đơn vị**
### Formats output:
- **JSON:** Dữ liệu thô từ API
- **CSV:** Dữ liệu đã format cho Excel
- **Markdown:** JIRA task report

---

## 🎉 Tính năng đã tích hợp

### ✅ Retry tool với update tự động
- Tự động cập nhật `crawl_results.json` sau retry
- Không cần chạy update thủ công
- Tính lại success_count và error_count

### ✅ Curl command logging
- Lưu đầy đủ curl commands với token và headers
- Dễ debug và test thủ công
- Format chuẩn để copy-paste

### ✅ Error handling
- Phân loại lỗi: token required, HTTP 304, API error
- Xử lý cache tự động
- Retry mechanism mạnh mẽ

### ✅ User experience
- Menu interactive dễ sử dụng
- Workflow suggestions
- Auto-progression giữa các bước

---

**🚀 Sẵn sàng sử dụng! Chạy `python3 vnsdi_toolkit.py` để bắt đầu.** 