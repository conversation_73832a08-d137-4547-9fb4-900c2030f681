#!/usr/bin/env python3
"""
Tool to crawl Vietnamese administrative division data from VNSDI API
"""

import requests
import json
import urllib.parse
from typing import Dict, Any, Optional, List
import time
import sys

class VNSDIDataCrawler:
    def __init__(self):
        self.base_url = "https://vnsdi.mae.gov.vn/basemap/rest/services/34DVHC/MapServer/0/query"
        self.token = "ExStvSdFXfM9AqU46uMerpamG7Snw7xZmlRf8VsRpynP4ggdhKW7O5lIcaaa7PcGl56gL1vYn4HYuSAi06zhMA.."
        
        # Headers from the curl command
        self.headers = {
              'accept': '*/*',
                'accept-language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
                'if-none-match': '"srZWYJIfFjETFfsz_1752399901"',
                'priority': 'u=1, i',
                'referer': 'https://vnsdi.mae.gov.vn/bandonen/',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'Cookie': 'AGS_ROLES="sym-gcm|1|y64cqvJ9gXTzM9iRxlEz9g==|dxbHs9n4KABMcj8LE6FuNLTDEnfl1QxnCoiRQbEeJWlqc5enfYSA6k-qoEz_VIACUb-y"; AGS_ROLES="sym-gcm|1|gr-RVE9eQ6G1FZnSphuFxQ==|l3f6bYO1N68Ytj1MxeTnG6xQ3ihHz777mTa3laqQl0jjfryfB1fSsQUSgTI-B8SAZ_iU"'
        }
    
    def get_province_data(self, province_code: str = "04") -> Dict[str, Any]:
        """
        Get data for a specific province by code
        
        Args:
            province_code: Province code (e.g., "04" for Hà Nội)
            
        Returns:
            Dictionary containing the API response
        """
        
        # Parameters for the API request
        params = {
            'returnGeometry': 'true',
            'where': f"MATINH='{province_code}'",
            'outSR': '4326',
            'outFields': '*',
            'token': self.token,
            'f': 'json'
        }
        
        try:
            print(f"Requesting data for province code: {province_code}")
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            
            data = response.json()
            
            # Check if the response contains features
            if 'features' in data:
                print(f"Successfully retrieved {len(data['features'])} features")
                return data
            else:
                print("Warning: No features found in response")
                return data
                
        except requests.RequestException as e:
            print(f"Error making request: {e}")
            return {'error': str(e)}
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            # Only show raw response if it's not too long
            if len(response.text) < 1000:
                print(f"Raw response text: {response.text}")
            return {'error': f'JSON decode error: {str(e)}'}
    
    def save_to_file(self, data: Dict[str, Any], filename: str = "vnsdi_data.json"):
        """
        Save data to a JSON file
        
        Args:
            data: Data to save
            filename: Output filename
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"Data saved to {filename}")
        except Exception as e:
            print(f"Error saving file: {e}")


def main():
    """
    Main function to run the crawler
    """
    crawler = VNSDIDataCrawler()
    

if __name__ == "__main__":
    main() 